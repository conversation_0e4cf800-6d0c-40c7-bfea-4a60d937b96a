# Last edit by <PERSON><PERSON><PERSON>, 24.11.4 
# 该脚本用以生成fake的SIST1-D区的1～4层
import xml.etree.ElementTree as ET
from xml.dom import minidom

def generate_unique_id(existing_ids):
    new_id = max(existing_ids) + 1
    while new_id in existing_ids:
        new_id += 1
    return new_id

def prettify(elem):
    """将ElementTree对象转换为格式化的XML字符串"""
    rough_string = ET.tostring(elem, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def duplicate_level_2_to_higher_levels(input_file, output_file):
    # 解析 XML 文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 获取所有现有的ID
    existing_ids = {int(way.attrib['id']) for way in root.findall('way')}
    
    # 创建列表存储新元素
    new_elements = []
    
    # 遍历所有 'way' 元素，查找 'level' 标签为 2 的元素
    for way in root.findall('way'):
        level_2_found = False
        for tag in way.findall('tag'):
            if tag.attrib.get('k') == 'level' and tag.attrib.get('v') == '2':
                level_2_found = True
                break
        
        if level_2_found:
            # 为level 3和4分别创建副本
            for new_level in ['3', '4']:
                new_way = ET.Element(way.tag)
                
                # 生成新的唯一ID
                new_id = generate_unique_id(existing_ids)
                existing_ids.add(new_id)
                new_way.set('id', str(new_id))
                new_way.set('action', 'modify')
                new_way.set('visible', 'true')
                
                # 复制所有子元素
                for child in way:
                    new_child = ET.Element(child.tag, child.attrib)
                    if child.tag == 'tag' and child.attrib.get('k') == 'level':
                        new_child.set('v', new_level)
                    new_way.append(new_child)
                
                new_elements.append(new_way)
    
    # 将新元素添加到根元素
    for new_elem in new_elements:
        root.append(new_elem)
    
    # 使用prettify函数格式化XML并写入文件
    pretty_xml = prettify(root)
    
    # 写入文件时去除空行
    with open(output_file, 'w', encoding='utf-8') as f:
        # 添加XML声明
        f.write('<?xml version="1.0" encoding="utf-8"?>\n')
        # 写入格式化的内容，去除空行
        clean_xml = '\n'.join(line for line in pretty_xml.split('\n') if line.strip())
        f.write(clean_xml)

if __name__ == '__main__':
    input_file = '/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_2F_D.osm'
    output_file = '/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_ALL_F_D.osm'
    duplicate_level_2_to_higher_levels(input_file, output_file)