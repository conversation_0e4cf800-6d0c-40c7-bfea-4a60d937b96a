import xml.etree.ElementTree as ET

def clean_empty_ways(input_file, output_file):
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 统计信息
    total_ways = 0
    removed_ways = 0
    
    # 找到所有way元素
    ways = root.findall(".//way")
    total_ways = len(ways)
    
    # 记录要删除的way
    ways_to_remove = []
    
    # 检查每个way
    for way in ways:
        # 查找该way的所有tag
        tags = way.findall('tag')
        if len(tags) == 0:
            ways_to_remove.append(way)
            removed_ways += 1
    
    # 删除没有tag的way
    for way in ways_to_remove:
        root.remove(way)
    
    # 打印统计信息
    print(f"Total number of ways: {total_ways}")
    print(f"Number of ways removed: {removed_ways}")
    print(f"Remaining ways: {total_ways - removed_ways}")
    
    # 保存修改后的文件
    tree.write(output_file, encoding='UTF-8', xml_declaration=True)
    print(f"Cleaned file saved to: {output_file}")

def verify_file(file_path):
    """验证文件，确保没有空way"""
    tree = ET.parse(file_path)
    root = tree.getroot()
    
    empty_ways = 0
    for way in root.findall(".//way"):
        if len(way.findall('tag')) == 0:
            empty_ways += 1
    
    if empty_ways == 0:
        print("Verification passed: No empty ways found")
    else:
        print(f"Verification failed: Found {empty_ways} empty ways")

if __name__ == "__main__":
    # 文件路径
    input_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/semantic_tags/SIST1_SEM_Sem_right_1119.osm"    # 输入文件路径
    output_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/semantic_tags/SIST1_SEM_Sem_right_1119_cleaned.osm"  # 输出文件路径
    
    try:
        # 执行清理
        clean_empty_ways(input_file, output_file)
        
        # 验证结果
        verify_file(output_file)
        
    except ET.ParseError:
        print("Error: Invalid XML file")
    except FileNotFoundError:
        print("Error: Input file not found")
    except Exception as e:
        print(f"Error: {str(e)}")