import xml.etree.ElementTree as ET
from collections import defaultdict

def convert_osm_format(input_file, output_file):
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 第一步：建立ID到name的映射
    id_to_name = {}
    # 遍历所有way元素
    for way in root.findall(".//way"):
        way_id = way.get('id')
        name_tag = way.find(".//tag[@k='name']")
        if name_tag is not None:
            id_to_name[way_id] = name_tag.get('v')
    
    # 第二步：转换room的parent引用和passage的from/to引用
    for way in root.findall(".//way"):
        tags = way.findall('.//tag')
        for tag in tags:
            # 处理room的parent引用
            if tag.get('k') == 'osmAG:parent':
                parent_id = tag.get('v')
                if parent_id in id_to_name:
                    tag.set('v', id_to_name[parent_id])
                    
            # 处理passage的from/to引用
            elif tag.get('k') in ['osmAG:from', 'osmAG:to']:
                ref_id = tag.get('v')
                if ref_id in id_to_name:
                    tag.set('v', id_to_name[ref_id])
    
    # 保存修改后的文件
    tree.write(output_file, encoding='UTF-8', xml_declaration=True)

# 使用示例
if __name__ == "__main__":
    input_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/fix_id/SIST1_1plus2_D.osm"    # 旧版本地图文件路径
    output_file = "/home/<USER>/osmAG_ws/src/area_graph_data_parser/data/semantic_tags/chengqian.osm"   # 新版本地图文件路径
    convert_osm_format(input_file, output_file)