import xml.etree.ElementTree as ET
from collections import defaultdict

def convert_to_old_format(input_file, output_file):
    # 解析XML文件
    tree = ET.parse(input_file)
    root = tree.getroot()
    
    # 第一步：建立更详细的映射
    name_to_original_id = {}
    level_name_to_id = defaultdict(dict)  # {level: {name: id}}
    
    # 首先遍历所有way元素，建立带层级的映射
    for way in root.findall(".//way"):
        way_id = way.get('id')
        name_tag = way.find(".//tag[@k='name']")
        type_tag = way.find(".//tag[@k='osmAG:areaType']")
        level_tag = way.find(".//tag[@k='level']")
        
        if name_tag is not None:
            name = name_tag.get('v')
            name_to_original_id[name] = way_id  # 保持原有的映射
            
            # 对于电梯和楼梯，建立带层级的映射
            if type_tag is not None and level_tag is not None:
                if type_tag.get('v') in ['elevator', 'stairs']:
                    level = level_tag.get('v')
                    level_name_to_id[level][name] = way_id
    
    # 第二步：更新引用
    conversion_count = 0
    for way in root.findall(".//way"):
        tags = way.findall('.//tag')
        way_level = None
        is_passage = False
        from_to_tags = []
        
        # 首先确定这个way的level和类型
        for tag in tags:
            if tag.get('k') == 'level':
                way_level = tag.get('v')
            elif tag.get('k') == 'osmAG:type' and tag.get('v') == 'passage':
                is_passage = True
            elif tag.get('k') in ['osmAG:from', 'osmAG:to']:
                from_to_tags.append(tag)
        
        # 处理每个tag
        for tag in tags:
            # 处理parent引用
            if tag.get('k') == 'osmAG:parent':
                parent_name = tag.get('v')
                if parent_name in name_to_original_id:
                    old_value = tag.get('v')
                    tag.set('v', name_to_original_id[parent_name])
                    conversion_count += 1
                    print(f"Converted parent reference: {old_value} -> {name_to_original_id[parent_name]}")
                else:
                    print(f"Warning: Cannot find original ID for parent name: {parent_name}")
            
            # 处理passage的from/to引用
            elif tag.get('k') in ['osmAG:from', 'osmAG:to']:
                ref_name = tag.get('v')
                if is_passage and way_level is not None:
                    # 检查是否是连接到电梯或楼梯的引用
                    if ref_name in level_name_to_id[way_level]:
                        # 使用同一层的电梯/楼梯ID
                        old_value = tag.get('v')
                        tag.set('v', level_name_to_id[way_level][ref_name])
                        conversion_count += 1
                        print(f"Converted {tag.get('k')} reference for vertical transport: {old_value} -> {level_name_to_id[way_level][ref_name]}")
                        continue
                
                # 如果不是电梯/楼梯或找不到对应层的ID，使用原有逻辑
                if ref_name in name_to_original_id:
                    old_value = tag.get('v')
                    tag.set('v', name_to_original_id[ref_name])
                    conversion_count += 1
                    print(f"Converted {tag.get('k')} reference: {old_value} -> {name_to_original_id[ref_name]}")
                else:
                    print(f"Warning: Cannot find original ID for reference name: {ref_name}")
    
    # 第三步：生成转换日志
    with open('conversion_mapping.log', 'w', encoding='utf-8') as f:
        f.write("Name to Original ID mapping:\n")
        for name, original_id in name_to_original_id.items():
            f.write(f"{name}: {original_id}\n")
        
        f.write("\nLevel-specific mappings for vertical transport:\n")
        for level, mappings in level_name_to_id.items():
            f.write(f"\nLevel {level}:\n")
            for name, id in mappings.items():
                f.write(f"  {name}: {id}\n")
        
        f.write(f"\nTotal conversions: {conversion_count}\n")
    
    # 保存修改后的文件
    tree.write(output_file, encoding='UTF-8', xml_declaration=True)

def verify_conversion(file_path):
    """验证转换后的文件正确性"""
    tree = ET.parse(file_path)
    root = tree.getroot()
    
    errors = []
    references = defaultdict(list)
    
    # 收集所有的way ID
    valid_ids = {way.get('id') for way in root.findall(".//way")}
    
    # 检查所有引用
    for way in root.findall(".//way"):
        way_id = way.get('id')
        tags = way.findall('.//tag')
        
        for tag in tags:
            if tag.get('k') in ['osmAG:parent', 'osmAG:from', 'osmAG:to']:
                ref_id = tag.get('v')
                references[ref_id].append(f"way[id={way_id}]")
                
                if ref_id not in valid_ids:
                    errors.append(f"Invalid reference: {ref_id} in way {way_id}")
    
    # 生成引用关系报告
    with open('reference_check.log', 'w', encoding='utf-8') as f:
        f.write("Reference relationships:\n")
        for ref_id, ways in references.items():
            f.write(f"\nID {ref_id} is referenced by:\n")
            for way in ways:
                f.write(f"  - {way}\n")
    
    return errors

if __name__ == "__main__":
    input_file = "/home/<USER>/AGLoc_ws/src/area_graph_data_parser/data/semantic_tags/SIST1_D_F2.osm"    # 新版本地图文件路径
    output_file = "/home/<USER>/AGLoc_ws/src/area_graph_data_parser/data/fix_id/SIST1_D_F2.osm"   # 旧版本地图文件路径
    
    try:
        # 执行转换
        convert_to_old_format(input_file, output_file)
        print("\nConversion completed. Check conversion_mapping.log for details.")
        
        # 验证转换结果
        errors = verify_conversion(output_file)
        if errors:
            print("\nWarning: Found following issues:")
            for error in errors:
                print(error)
        else:
            print("\nVerification passed: All references are valid.")
            
    except Exception as e:
        print(f"Error during conversion: {str(e)}")